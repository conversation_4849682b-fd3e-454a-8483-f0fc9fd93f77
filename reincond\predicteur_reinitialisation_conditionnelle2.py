#!/usr/bin/env python3
"""
PRÉDICTEUR À RÉINITIALISATION CONDITIONNELLE - THÉORIE LUPASCO
=============================================================

Implémente le mécanisme révolutionnaire de réinitialisation conditionnelle :
- État initial = Point Zéro universel
- Prédiction INCORRECTE → métriques évoluent selon réalité
- Prédiction CORRECTE → RESET complet vers Point Zéro universel

Point Zéro Universel (6,632,137 mains) :
- 0: 49.71%, 1: 50.29%
- A: 37.86%, B: 31.75%, C: 30.39%  
- BANKER: 45.83%, PLAYER: 44.64%, TIE: 9.53%
"""

import json
import numpy as np
from datetime import datetime
from typing import List, Dict, Tuple, Optional

class PredicteurReinitialisation:
    """
    Prédicteur avec mécanisme de réinitialisation conditionnelle.
    """
    
    def __init__(self):
        # POINT ZÉRO UNIVERSEL (signatures absolues)
        self.POINT_ZERO_UNIVERSEL = {
            '0': 0.4971,      # 49.71%
            '1': 0.5029,      # 50.29%
            'A': 0.3786,      # 37.86%
            'B': 0.3175,      # 31.75%
            'C': 0.3039,      # 30.39%
            'BANKER': 0.4583, # 45.83%
            'PLAYER': 0.4464, # 44.64%
            'TIE': 0.0953     # 9.53%
        }
        
        # État actuel des métriques (commence au Point Zéro)
        self.metriques_actuelles = self.POINT_ZERO_UNIVERSEL.copy()
        
        # Historique pour analyse
        self.historique_predictions = []
        self.historique_metriques = []
        self.nb_reinitialisations = 0
        self.nb_predictions_totales = 0


        
        # Paramètres de prédiction
        self.seuil_deviation_minimum = 0.02  # 2% d'écart minimum pour prédire
        self.seuil_force_rappel = 0.01       # 1% de différence minimum entre forces
        self.seuil_tie_minimum = 0.05        # 5% de déviation minimum pour prédire TIE
    
    def calculer_proportions_reelles(self, mains_partie: List[Dict]) -> Dict:
        """
        Calcule les proportions réelles basées sur les mains actuelles.
        """
        if not mains_partie:
            return self.POINT_ZERO_UNIVERSEL.copy()
        
        # Compteurs
        compteurs = {
            '0': 0, '1': 0,
            'A': 0, 'B': 0, 'C': 0,
            'BANKER': 0, 'PLAYER': 0, 'TIE': 0
        }
        
        # Comptage des occurrences
        for main in mains_partie:
            index1 = str(main.get('index1', ''))
            index2 = main.get('index2', '')
            index3 = main.get('index3', '')
            
            if index1 in compteurs:
                compteurs[index1] += 1
            if index2 in compteurs:
                compteurs[index2] += 1
            if index3 in compteurs:
                compteurs[index3] += 1
        
        # Calcul des proportions
        total_mains = len(mains_partie)
        proportions = {}
        
        for element in compteurs:
            proportions[element] = compteurs[element] / total_mains
        
        return proportions
    
    def calculer_forces_rappel(self) -> Dict:
        """
        Calcule les forces de rappel vers le Point Zéro pour chaque élément.
        """
        forces = {}
        
        for element, valeur_actuelle in self.metriques_actuelles.items():
            valeur_point_zero = self.POINT_ZERO_UNIVERSEL[element]
            deviation = abs(valeur_actuelle - valeur_point_zero)
            
            # Force de rappel proportionnelle à la déviation
            force_rappel = deviation * (valeur_point_zero - valeur_actuelle)
            forces[element] = force_rappel
        
        return forces
    
    def predire_index3_suivant(self, mains_partie: List[Dict]) -> Tuple[Optional[str], Dict]:
        """
        Prédit INDEX3 suivant basé sur les forces de rappel vers Point Zéro.

        RÈGLE FONDAMENTALE :
        - Main 1 : AUCUNE PRÉDICTION (observer la première déviation)
        - Main 2+ : Prédictions pour ramener vers Point Zéro
        """
        # RÈGLE : Pas de prédiction sur la première main
        if len(mains_partie) <= 1:
            return None, {'raison': 'main_1_observation_seulement'}

        # Minimum 2 mains pour commencer les prédictions
        if len(mains_partie) < 2:
            return None, {'raison': 'pas_assez_mains'}

        # Calcul des proportions actuelles après la première main
        self.metriques_actuelles = self.calculer_proportions_reelles(mains_partie)

        # Calcul des déviations par rapport au Point Zéro
        deviations_index3 = {
            'BANKER': self.metriques_actuelles['BANKER'] - self.POINT_ZERO_UNIVERSEL['BANKER'],
            'PLAYER': self.metriques_actuelles['PLAYER'] - self.POINT_ZERO_UNIVERSEL['PLAYER'],
            'TIE': self.metriques_actuelles['TIE'] - self.POINT_ZERO_UNIVERSEL['TIE']
        }

        # Vérification qu'il y a une déviation significative
        deviation_max = max(abs(dev) for dev in deviations_index3.values())

        if deviation_max < self.seuil_deviation_minimum:
            return None, {'raison': 'deviation_insuffisante', 'deviation_max': deviation_max}

        # STRATÉGIE DE RAPPEL VERS POINT ZÉRO CORRIGÉE :
        # Pondérer les déviations par la probabilité de l'élément
        # pour éviter la sur-prédiction de TIE (rare mais souvent en-dessous)

        # Calcul des forces de rappel pondérées
        forces_ponderees = {}
        for element, deviation in deviations_index3.items():
            point_zero_element = self.POINT_ZERO_UNIVERSEL[element]

            # Force = déviation négative * probabilité de l'élément
            # Plus l'élément est probable ET en-dessous, plus la force est élevée
            if deviation < 0:  # Seulement si en-dessous du Point Zéro
                force_rappel = abs(deviation) * point_zero_element
                forces_ponderees[element] = force_rappel
            else:
                forces_ponderees[element] = 0

        # STRATÉGIE OPTIMISÉE : JAMAIS PRÉDIRE TIE
        # TIE a 0% de réussite et perturbe le système
        # Focus sur BANKER vs PLAYER uniquement

        # Exclure TIE des forces de rappel
        forces_banker_player = {
            'BANKER': forces_ponderees.get('BANKER', 0),
            'PLAYER': forces_ponderees.get('PLAYER', 0)
        }

        # Prédire BANKER ou PLAYER avec la plus forte force de rappel
        if any(force > 0 for force in forces_banker_player.values()):
            element_max_force = max(forces_banker_player.items(), key=lambda x: x[1])
            prediction = element_max_force[0]
        else:
            # Si aucun n'est en-dessous, prédire celui le plus en-dessous entre BANKER et PLAYER
            if deviations_index3['BANKER'] < deviations_index3['PLAYER']:
                prediction = 'BANKER'
            else:
                prediction = 'PLAYER'

        # Détails pour analyse
        details = {
            'metriques_actuelles': {
                'BANKER': self.metriques_actuelles['BANKER'],
                'PLAYER': self.metriques_actuelles['PLAYER'],
                'TIE': self.metriques_actuelles['TIE']
            },
            'point_zero_reference': {
                'BANKER': self.POINT_ZERO_UNIVERSEL['BANKER'],
                'PLAYER': self.POINT_ZERO_UNIVERSEL['PLAYER'],
                'TIE': self.POINT_ZERO_UNIVERSEL['TIE']
            },
            'deviations_point_zero': deviations_index3,
            'forces_ponderees': forces_ponderees,
            'prediction_choisie': prediction,
            'deviation_max': deviation_max,
            'strategie': 'rappel_pondere_vers_point_zero'
        }

        return prediction, details
    
    def evaluer_et_mettre_a_jour(self, mains_partie: List[Dict], prediction: str, 
                                index3_reel: str) -> Tuple[bool, bool, Dict]:
        """
        Évalue la prédiction et met à jour les métriques selon le mécanisme.
        
        Returns:
            (prediction_correcte, comptee, details)
        """
        self.nb_predictions_totales += 1
        
        # Règle TIE : si TIE survient, prédiction non comptée
        if index3_reel == 'TIE':
            # Mise à jour des métriques avec la nouvelle main
            self.metriques_actuelles = self.calculer_proportions_reelles(mains_partie)
            
            return False, False, {
                'raison': 'tie_non_compte',
                'metriques_apres': self.metriques_actuelles.copy(),
                'reinitialisation': False
            }
        
        # Évaluation de la prédiction
        prediction_correcte = (prediction == index3_reel)
        reinitialisation_effectuee = False
        raison_reinitialisation = ""

        if prediction_correcte:
            # RÉINITIALISATION CONDITIONNELLE !
            self.metriques_actuelles = self.POINT_ZERO_UNIVERSEL.copy()
            self.nb_reinitialisations += 1



            reinitialisation_effectuee = True
            raison_reinitialisation = "prediction_correcte"

            details = {
                'raison': raison_reinitialisation,
                'metriques_apres': self.metriques_actuelles.copy(),
                'reinitialisation': True,
                'nb_reinitialisations': self.nb_reinitialisations
            }
        else:
            # Mise à jour avec proportions réelles
            self.metriques_actuelles = self.calculer_proportions_reelles(mains_partie)

            details = {
                'raison': 'prediction_incorrecte',
                'metriques_apres': self.metriques_actuelles.copy(),
                'reinitialisation': False
            }
        
        # Sauvegarde historique
        historique_entry = {
            'prediction': prediction,
            'reel': index3_reel,
            'correcte': prediction_correcte,
            'nb_mains': len(mains_partie),
            'reinitialisation_effectuee': reinitialisation_effectuee,
            'raison_reinitialisation': raison_reinitialisation
        }

        self.historique_predictions.append(historique_entry)
        
        self.historique_metriques.append(self.metriques_actuelles.copy())
        
        return prediction_correcte, True, details

    def reinitialiser_debut_partie(self):
        """
        Réinitialisation automatique au début de chaque partie (main null).

        RÈGLE LUPASCO :
        - Chaque partie commence avec les métriques Point Zéro universel
        """
        self.metriques_actuelles = self.POINT_ZERO_UNIVERSEL.copy()
        self.nb_reinitialisations += 1

        # Enregistrement de la réinitialisation automatique
        self.historique_predictions.append({
            'prediction': 'REINIT_DEBUT_PARTIE',
            'reel': 'MAIN_NULL',
            'correcte': True,
            'nb_mains': 0,
            'reinitialisation_effectuee': True,
            'raison_reinitialisation': 'debut_partie_automatique'
        })

        self.historique_metriques.append(self.metriques_actuelles.copy())

    def obtenir_statistiques(self) -> Dict:
        """Retourne les statistiques du prédicteur."""
        if not self.historique_predictions:
            return {'erreur': 'aucune_prediction'}
        
        predictions_comptees = [p for p in self.historique_predictions if p.get('comptee', True)]
        
        if not predictions_comptees:
            return {'erreur': 'aucune_prediction_comptee'}
        
        nb_correctes = sum(1 for p in predictions_comptees if p['correcte'])
        nb_totales = len(predictions_comptees)
        
        # Compter les types de réinitialisations
        nb_reinit_debut_partie = sum(1 for p in self.historique_predictions
                                   if p.get('raison_reinitialisation') == 'debut_partie_automatique')

        nb_reinit_echecs = sum(1 for p in self.historique_predictions
                             if p.get('raison_reinitialisation', '').startswith('echecs_consecutifs'))

        nb_reinit_correctes = sum(1 for p in self.historique_predictions
                                if p.get('raison_reinitialisation') == 'prediction_correcte')

        return {
            'nb_predictions_totales': self.nb_predictions_totales,
            'nb_predictions_comptees': nb_totales,
            'nb_predictions_correctes': nb_correctes,
            'taux_reussite': nb_correctes / nb_totales * 100,
            'nb_reinitialisations': self.nb_reinitialisations,
            'taux_reinitialisation': self.nb_reinitialisations / nb_totales * 100 if nb_totales > 0 else 0,
            'nb_reinit_debut_partie': nb_reinit_debut_partie,
            'nb_reinit_echecs_consecutifs': nb_reinit_echecs,
            'nb_reinit_predictions_correctes': nb_reinit_correctes,
            'echecs_consecutifs_actuels': self.echecs_consecutifs,
            'predictions_correctes_consecutives_actuelles': self.predictions_correctes_consecutives,
            'predictions_incorrectes_consecutives_actuelles': self.predictions_incorrectes_consecutives,
            'metriques_finales': self.metriques_actuelles.copy(),
            'ecart_point_zero_final': {
                element: abs(self.metriques_actuelles[element] - self.POINT_ZERO_UNIVERSEL[element])
                for element in self.POINT_ZERO_UNIVERSEL
            }
        }

def tester_predicteur_reinitialisation():
    """Test du prédicteur à réinitialisation conditionnelle."""
    
    print("🌊 PRÉDICTEUR À RÉINITIALISATION CONDITIONNELLE - VERSION AMÉLIORÉE")
    print("=" * 65)
    print("🆕 NOUVELLES FONCTIONNALITÉS:")
    print("   ├─ 🏁 Réinitialisation automatique au début de chaque partie")
    print("   └─ ❌ Réinitialisation après 2 échecs consécutifs (MODIFIÉ !)")
    print()
    
    # Chargement du dataset
    print("📂 Chargement du dataset...")
    try:
        with open('dataset_baccarat_lupasco_20250630_102307.json', 'r', encoding='utf-8') as f:
            dataset = json.load(f)
    except Exception as e:
        print(f"❌ Erreur chargement dataset: {e}")
        return
    
    parties = dataset.get('parties', [])
    print(f"✅ Dataset chargé: {len(parties)} parties")
    
    # Initialisation du prédicteur
    predicteur = PredicteurReinitialisation()
    
    # Test sur 10,000 parties avec 2 échecs consécutifs
    print(f"\n🎯 Test sur 10,000 parties (2 échecs consécutifs)...")

    nb_predictions_totales = 0
    nb_predictions_comptees = 0
    nb_predictions_correctes = 0

    for i, partie in enumerate(parties[:10000]):
        mains = partie.get('mains', [])
        if len(mains) < 10:
            continue

        # Affichage du progrès tous les 1000 parties
        if (i + 1) % 1000 == 0:
            print(f"   Partie {i+1}/10,000 ({len(mains)} mains)...")
        elif (i + 1) <= 10:
            print(f"   Partie {i+1}/10,000 ({len(mains)} mains)...")
        
        # RÉINITIALISATION AUTOMATIQUE AU DÉBUT DE CHAQUE PARTIE (MAIN NULL)
        predicteur.reinitialiser_debut_partie()

        # ÉTAPE 1 : Observer la main 1 pour calculer les déviations initiales
        if len(mains) >= 1:
            # Calculer les métriques après observation de la main 1
            mains_observees = mains[:1]  # Seulement la main 1
            predicteur.metriques_actuelles = predicteur.calculer_proportions_reelles(mains_observees)

        # ÉTAPE 2 : Prédictions à partir de la main 2
        # RÈGLE : Commencer à partir de la 2ème main (j=1, prédire main 2)
        for j in range(1, len(mains)):  # j=1 → prédire main 2, j=2 → prédire main 3, etc.
            mains_actuelles = mains[:j]  # Mains connues jusqu'à j-1

            # Prédiction pour la main j
            prediction, details_pred = predicteur.predire_index3_suivant(mains_actuelles)

            if prediction:
                nb_predictions_totales += 1
                index3_reel = mains[j].get('index3')  # Résultat réel de la main j

                # Évaluation et mise à jour
                mains_avec_nouvelle = mains[:j+1]  # Inclure la main j dans l'historique
                correcte, comptee, details_eval = predicteur.evaluer_et_mettre_a_jour(
                    mains_avec_nouvelle, prediction, index3_reel
                )

                if comptee:
                    nb_predictions_comptees += 1
                    if correcte:
                        nb_predictions_correctes += 1
    
    # Résultats
    print(f"\n✅ Test terminé!")
    print(f"📊 Prédictions totales: {nb_predictions_totales}")
    print(f"📊 Prédictions comptées: {nb_predictions_comptees}")
    print(f"📊 Prédictions correctes: {nb_predictions_correctes}")
    
    if nb_predictions_comptees > 0:
        taux_reussite = nb_predictions_correctes / nb_predictions_comptees * 100
        stats = predicteur.obtenir_statistiques()
        print(f"🎯 Taux de réussite: {taux_reussite:.2f}%")
        print(f"🔄 Réinitialisations totales: {predicteur.nb_reinitialisations}")
        print(f"   ├─ 🏁 Début de partie: {stats['nb_reinit_debut_partie']}")
        print(f"   ├─ ❌ Échecs consécutifs: {stats['nb_reinit_echecs_consecutifs']}")
        print(f"   └─ ✅ Prédictions correctes: {stats['nb_reinit_predictions_correctes']}")
        print(f"🔄 Taux réinit: {predicteur.nb_reinitialisations/nb_predictions_comptees*100:.1f}%")
        print(f"⚠️  Échecs consécutifs actuels: {stats['echecs_consecutifs_actuels']}")
        print(f"✅ Succès consécutifs actuels: {stats['predictions_correctes_consecutives_actuelles']}")
        print(f"❌ Échecs consécutifs actuels: {stats['predictions_incorrectes_consecutives_actuelles']}")
    
    # Sauvegarde des résultats
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fichier_resultats = f"resultats_reinitialisation_{timestamp}.json"
    
    resultats = {
        'statistiques': predicteur.obtenir_statistiques(),
        'historique_predictions': predicteur.historique_predictions,  # TOUT L'HISTORIQUE
        'point_zero_universel': predicteur.POINT_ZERO_UNIVERSEL,
        'timestamp': timestamp
    }
    
    with open(fichier_resultats, 'w', encoding='utf-8') as f:
        json.dump(resultats, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Résultats sauvegardés: {fichier_resultats}")
    print("\n🎊 RÉVOLUTION LUPASCO - RÉINITIALISATION CONDITIONNELLE TESTÉE!")

if __name__ == "__main__":
    tester_predicteur_reinitialisation()
