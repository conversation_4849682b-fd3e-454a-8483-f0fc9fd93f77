#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DIAGNOSTIC LÉGER DU PROBLÈME N/A
================================

Diagnostic rapide pour identifier le problème sans charger tout le dataset
"""

import os
import json

def diagnostic_fichiers():
    """Diagnostic des fichiers présents"""
    print("🔍 DIAGNOSTIC DES FICHIERS")
    print("=" * 40)
    
    fichiers_cles = [
        'dataset_baccarat_lupasco_20250702_000230.json',
        'analyseur_scientifique_revolutionnaire.py',
        'rapport_revolutionnaire_baccarat_lupasco.txt'
    ]
    
    for fichier in fichiers_cles:
        if os.path.exists(fichier):
            taille = os.path.getsize(fichier)
            print(f"✅ {fichier} : {taille:,} bytes ({taille/1024/1024:.1f} MB)")
        else:
            print(f"❌ {fichier} : MANQUANT")

def diagnostic_dataset_leger():
    """Diagnostic léger du dataset (premières lignes seulement)"""
    print(f"\n🔍 DIAGNOSTIC DATASET (ÉCHANTILLON)")
    print("=" * 50)
    
    try:
        # Lire seulement les premières lignes pour éviter le blocage
        with open('dataset_baccarat_lupasco_20250702_000230.json', 'r', encoding='utf-8') as f:
            # Lire les 1000 premiers caractères
            debut = f.read(1000)
            print(f"📋 Début du fichier JSON :")
            print(debut[:500] + "..." if len(debut) > 500 else debut)
            
    except Exception as e:
        print(f"❌ Erreur lecture dataset : {e}")

def diagnostic_rapport_actuel():
    """Diagnostic du rapport actuel généré"""
    print(f"\n🔍 DIAGNOSTIC RAPPORT ACTUEL")
    print("=" * 40)
    
    try:
        with open('rapport_revolutionnaire_baccarat_lupasco.txt', 'r', encoding='utf-8') as f:
            contenu = f.read()
            
        print(f"📄 Taille du rapport : {len(contenu)} caractères")
        
        # Rechercher les valeurs N/A
        nb_na = contenu.count('N/A')
        nb_zero = contenu.count('0.000000')
        
        print(f"🚨 Problèmes détectés :")
        print(f"   • Occurrences 'N/A' : {nb_na}")
        print(f"   • Occurrences '0.000000' : {nb_zero}")
        
        # Extraire les sections problématiques
        lignes = contenu.split('\n')
        lignes_na = [ligne for ligne in lignes if 'N/A' in ligne]
        
        print(f"\n📋 LIGNES AVEC N/A :")
        for ligne in lignes_na[:10]:  # Premières 10
            print(f"   {ligne.strip()}")
        
        # Rechercher la section SYNC/DESYNC
        for i, ligne in enumerate(lignes):
            if 'ÉQUILIBRE SYNC/DESYNC' in ligne:
                print(f"\n📊 SECTION SYNC/DESYNC (lignes {i}-{i+10}) :")
                for j in range(i, min(i+10, len(lignes))):
                    print(f"   {lignes[j].strip()}")
                break
                
    except Exception as e:
        print(f"❌ Erreur lecture rapport : {e}")

def diagnostic_execution_analyseur():
    """Diagnostic de l'exécution de l'analyseur"""
    print(f"\n🔍 DIAGNOSTIC EXÉCUTION ANALYSEUR")
    print("=" * 50)
    
    print(f"🎯 PROBLÈME IDENTIFIÉ :")
    print(f"   L'analyseur extrait 663,131 mains mais produit N/A")
    print(f"   Cela suggère que :")
    print(f"   1. L'extraction réussit (comptage correct)")
    print(f"   2. Mais les calculs échouent (valeurs N/A)")
    
    print(f"\n🔍 HYPOTHÈSES PRINCIPALES :")
    print(f"   A. Types de données incompatibles")
    print(f"      → INDEX1 contient des strings au lieu d'entiers")
    print(f"      → Comparaisons '0' vs 0 échouent")
    
    print(f"   B. Séquences vides après extraction")
    print(f"      → self.sequences['index1'] est vide")
    print(f"      → Calculs sur array vide = N/A")
    
    print(f"   C. Erreurs silencieuses dans les calculs")
    print(f"      → Exceptions catchées mais non affichées")
    print(f"      → Valeurs par défaut (N/A) utilisées")
    
    print(f"\n🔧 SOLUTIONS RECOMMANDÉES :")
    print(f"   1. Ajouter debug prints dans analyser_equilibre_sync_desync()")
    print(f"   2. Vérifier types de données dans INDEX1")
    print(f"   3. Valider que self.sequences['index1'] n'est pas vide")
    print(f"   4. Tracer l'exécution complète avec try/except détaillés")

def generer_patch_debug():
    """Génère un patch pour ajouter du debug à l'analyseur"""
    print(f"\n🔧 GÉNÉRATION PATCH DEBUG")
    print("=" * 40)
    
    patch_debug = '''
# PATCH DEBUG À AJOUTER DANS analyser_equilibre_sync_desync()
# Après la ligne "seq_index1 = np.array(self.sequences['index1'])"

print(f"🔍 DEBUG SYNC/DESYNC :")
print(f"   • Taille séquence INDEX1 : {len(seq_index1)}")
print(f"   • Type données : {seq_index1.dtype}")
print(f"   • Échantillon : {seq_index1[:10]}")
print(f"   • Valeurs uniques : {np.unique(seq_index1)}")

# Test des comparaisons
test_sync = seq_index1 == 0
test_desync = seq_index1 == 1
print(f"   • Test == 0 : {np.sum(test_sync)} éléments")
print(f"   • Test == 1 : {np.sum(test_desync)} éléments")
'''
    
    print(f"📋 PATCH DEBUG :")
    print(patch_debug)
    
    return patch_debug

if __name__ == "__main__":
    diagnostic_fichiers()
    diagnostic_dataset_leger()
    diagnostic_rapport_actuel()
    diagnostic_execution_analyseur()
    generer_patch_debug()
    
    print(f"\n🎯 CONCLUSION :")
    print(f"   Le problème est probablement dans les types de données INDEX1")
    print(f"   Solution : Ajouter debug et corriger les conversions de types")
