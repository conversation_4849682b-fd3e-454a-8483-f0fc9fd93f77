#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PRÉDICTEUR RÉVOLUTIONNAIRE INDEX5 - ORDRE 3
============================================

Utilise les patterns d'ordre 3 découverts dans l'analyse révolutionnaire
pour prédire la troisième valeur d'INDEX5 connaissant les deux premières.

Basé sur la théorie des chaînes de Markov d'ordre supérieur et les matrices
de transition conditionnelles P(v3 | v1, v2).

Auteur: Analyseur Scientifique Révolutionnaire
Date: 2025-07-02
"""

import json
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, Counter
from analyseur_scientifique_revolutionnaire import AnalyseurScientifiqueRevolutionnaire

class PredicteurIndex5Ordre3:
    """
    Prédicteur révolutionnaire pour INDEX5 basé sur les patterns d'ordre 3
    """
    
    def __init__(self, analyseur: AnalyseurScientifiqueRevolutionnaire = None):
        """
        Initialise le prédicteur avec un analyseur existant ou en crée un nouveau
        
        Args:
            analyseur: Instance d'AnalyseurScientifiqueRevolutionnaire déjà entraînée
        """
        self.analyseur = analyseur
        self.patterns_ordre3 = None
        self.predicteur_fonction = None
        self.est_entraine = False
        
    def entrainer(self, fichier_donnees: str = None):
        """
        Entraîne le prédicteur sur les données de baccarat
        
        Args:
            fichier_donnees: Chemin vers le fichier de données (optionnel si analyseur fourni)
        """
        if self.analyseur is None:
            if fichier_donnees is None:
                raise ValueError("Fichier de données requis si aucun analyseur fourni")
            
            print("🔄 Création et entraînement de l'analyseur...")
            self.analyseur = AnalyseurScientifiqueRevolutionnaire()
            self.analyseur.charger_donnees(fichier_donnees)
            self.analyseur.extraire_sequences()
        
        print("🎯 Analyse des patterns d'ordre 3 INDEX5...")
        self.analyseur.analyser_avance()
        
        # Récupération des résultats d'analyse des patterns ordre 3
        if 'patterns_ordre3_index5' in self.analyseur.resultats_avances:
            self.patterns_ordre3 = self.analyseur.resultats_avances['patterns_ordre3_index5']
            self.est_entraine = True
            print("✅ Prédicteur entraîné avec succès !")
            self._afficher_statistiques_entrainement()
        else:
            raise RuntimeError("Échec de l'analyse des patterns d'ordre 3")
    
    def _afficher_statistiques_entrainement(self):
        """Affiche les statistiques d'entraînement"""
        if not self.patterns_ordre3:
            return
            
        print(f"\n📊 STATISTIQUES D'ENTRAÎNEMENT :")
        print(f"   • Triplets analysés : {self.patterns_ordre3.get('triplets_analyses', 0):,}")
        print(f"   • Contextes uniques : {self.patterns_ordre3.get('contextes_uniques', 0)}")
        print(f"   • Patterns déterministes (≥90%) : {self.patterns_ordre3.get('nb_patterns_deterministes', 0)}")
        print(f"   • Patterns probabilistes (≥60%) : {self.patterns_ordre3.get('nb_patterns_probabilistes', 0)}")
        print(f"   • Taux de prédictibilité : {self.patterns_ordre3.get('taux_predictibilite', 0):.3f}")
        print(f"   • Information mutuelle I(V3;V1,V2) : {self.patterns_ordre3.get('information_mutuelle', 0):.6f} bits")
        print(f"   • Efficacité de prédiction : {self.patterns_ordre3.get('efficacite_prediction', 0):.4f}")
    
    def predire(self, v1: str, v2: str) -> Dict[str, Any]:
        """
        Prédit la troisième valeur d'INDEX5 connaissant les deux premières
        
        Args:
            v1: Première valeur INDEX5 (format: "0_A_BANKER")
            v2: Deuxième valeur INDEX5 (format: "1_B_PLAYER")
            
        Returns:
            Dict contenant la prédiction et les informations associées
        """
        if not self.est_entraine:
            raise RuntimeError("Le prédicteur doit être entraîné avant utilisation")
        
        # Validation du format INDEX5
        format_valide = r'^[01]_[ABC]_(BANKER|PLAYER|TIE)$'
        import re
        if not re.match(format_valide, v1) or not re.match(format_valide, v2):
            raise ValueError(f"Format INDEX5 invalide. Attendu: '0_A_BANKER', reçu: '{v1}', '{v2}'")
        
        contexte = (v1, v2)
        
        # Recherche dans les patterns déterministes
        if contexte in self.patterns_ordre3.get('patterns_deterministes', {}):
            pattern = self.patterns_ordre3['patterns_deterministes'][contexte]
            return {
                'v3_predit': pattern['v3_predit'],
                'probabilite': pattern['probabilite'],
                'entropie': pattern['entropie'],
                'type_prediction': 'deterministe',
                'confiance': 'TRÈS ÉLEVÉE (≥90%)',
                'contexte': contexte
            }
        
        # Recherche dans les patterns probabilistes
        elif contexte in self.patterns_ordre3.get('patterns_probabilistes', {}):
            pattern = self.patterns_ordre3['patterns_probabilistes'][contexte]
            return {
                'v3_predit': pattern['v3_predit'],
                'probabilite': pattern['probabilite'],
                'entropie': pattern['entropie'],
                'type_prediction': 'probabiliste',
                'confiance': 'ÉLEVÉE (≥60%)',
                'contexte': contexte
            }
        
        # Recherche dans toutes les probabilités conditionnelles
        elif contexte in self.patterns_ordre3.get('probabilites_conditionnelles', {}):
            probs = self.patterns_ordre3['probabilites_conditionnelles'][contexte]
            v3_max = max(probs.items(), key=lambda x: x[1])
            entropie = self.patterns_ordre3.get('entropies_conditionnelles', {}).get(contexte, 0)
            
            return {
                'v3_predit': v3_max[0],
                'probabilite': v3_max[1],
                'entropie': entropie,
                'type_prediction': 'faible',
                'confiance': 'MODÉRÉE (<60%)',
                'contexte': contexte,
                'toutes_probabilites': probs
            }
        
        # Contexte jamais observé
        else:
            return {
                'v3_predit': None,
                'probabilite': 0.0,
                'entropie': None,
                'type_prediction': 'inconnu',
                'confiance': 'AUCUNE',
                'contexte': contexte,
                'erreur': 'Contexte jamais observé dans les données d\'entraînement'
            }
    
    def predire_sequence(self, sequence_index5: List[str]) -> List[Dict[str, Any]]:
        """
        Prédit une séquence complète d'INDEX5
        
        Args:
            sequence_index5: Liste des valeurs INDEX5 (minimum 2 valeurs)
            
        Returns:
            Liste des prédictions pour chaque position ≥ 3
        """
        if len(sequence_index5) < 2:
            raise ValueError("Séquence trop courte, minimum 2 valeurs requises")
        
        predictions = []
        
        for i in range(2, len(sequence_index5)):
            v1 = sequence_index5[i-2]
            v2 = sequence_index5[i-1]
            v3_reel = sequence_index5[i] if i < len(sequence_index5) else None
            
            prediction = self.predire(v1, v2)
            prediction['position'] = i
            prediction['v3_reel'] = v3_reel
            
            if v3_reel:
                prediction['prediction_correcte'] = (prediction['v3_predit'] == v3_reel)
            
            predictions.append(prediction)
        
        return predictions
    
    def evaluer_performance(self, sequence_test: List[str]) -> Dict[str, float]:
        """
        Évalue la performance du prédicteur sur une séquence de test
        
        Args:
            sequence_test: Séquence INDEX5 de test
            
        Returns:
            Métriques de performance
        """
        predictions = self.predire_sequence(sequence_test)
        
        if not predictions:
            return {'erreur': 'Aucune prédiction possible'}
        
        # Calcul des métriques
        total_predictions = len(predictions)
        predictions_correctes = sum(1 for p in predictions if p.get('prediction_correcte', False))
        predictions_deterministes = sum(1 for p in predictions if p['type_prediction'] == 'deterministe')
        predictions_probabilistes = sum(1 for p in predictions if p['type_prediction'] == 'probabiliste')
        
        accuracy_globale = predictions_correctes / total_predictions if total_predictions > 0 else 0
        
        # Accuracy par type de prédiction
        accuracy_deterministe = 0
        accuracy_probabiliste = 0
        
        if predictions_deterministes > 0:
            correctes_det = sum(1 for p in predictions 
                              if p['type_prediction'] == 'deterministe' and p.get('prediction_correcte', False))
            accuracy_deterministe = correctes_det / predictions_deterministes
        
        if predictions_probabilistes > 0:
            correctes_prob = sum(1 for p in predictions 
                               if p['type_prediction'] == 'probabiliste' and p.get('prediction_correcte', False))
            accuracy_probabiliste = correctes_prob / predictions_probabilistes
        
        return {
            'accuracy_globale': accuracy_globale,
            'accuracy_deterministe': accuracy_deterministe,
            'accuracy_probabiliste': accuracy_probabiliste,
            'total_predictions': total_predictions,
            'predictions_correctes': predictions_correctes,
            'predictions_deterministes': predictions_deterministes,
            'predictions_probabilistes': predictions_probabilistes,
            'taux_predictions_fortes': (predictions_deterministes + predictions_probabilistes) / total_predictions
        }
    
    def sauvegarder_modele(self, fichier: str):
        """Sauvegarde le modèle entraîné"""
        if not self.est_entraine:
            raise RuntimeError("Aucun modèle à sauvegarder")
        
        modele = {
            'patterns_ordre3': self.patterns_ordre3,
            'est_entraine': self.est_entraine,
            'version': '1.0'
        }
        
        with open(fichier, 'w', encoding='utf-8') as f:
            json.dump(modele, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Modèle sauvegardé : {fichier}")
    
    def charger_modele(self, fichier: str):
        """Charge un modèle pré-entraîné"""
        with open(fichier, 'r', encoding='utf-8') as f:
            modele = json.load(f)
        
        self.patterns_ordre3 = modele['patterns_ordre3']
        self.est_entraine = modele['est_entraine']
        
        print(f"✅ Modèle chargé : {fichier}")
        self._afficher_statistiques_entrainement()


def exemple_utilisation():
    """Exemple d'utilisation du prédicteur"""
    print("🎯 EXEMPLE D'UTILISATION DU PRÉDICTEUR INDEX5 ORDRE 3")
    print("=" * 60)
    
    # Création et entraînement du prédicteur
    predicteur = PredicteurIndex5Ordre3()
    
    # Supposons que nous avons un fichier de données
    # predicteur.entrainer("donnees_baccarat.json")
    
    # Exemple de prédiction
    # v1, v2 = "0_A_BANKER", "1_B_PLAYER"
    # prediction = predicteur.predire(v1, v2)
    # print(f"Prédiction pour ({v1}, {v2}) : {prediction}")


if __name__ == "__main__":
    exemple_utilisation()
