#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DIAGNOSTIC CRITIQUE DE L'ANALYSEUR
=================================

Script pour identifier précisément pourquoi l'analyseur produit des valeurs N/A
"""

import json
import numpy as np
from collections import Counter
import os

def diagnostic_dataset():
    """Diagnostic du dataset JSON"""
    print("🔍 DIAGNOSTIC CRITIQUE DU DATASET")
    print("=" * 50)
    
    dataset_path = "dataset_baccarat_lupasco_20250702_000230.json"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset non trouvé : {dataset_path}")
        return False
    
    # Taille du fichier
    taille = os.path.getsize(dataset_path)
    print(f"📁 Taille du dataset : {taille:,} bytes ({taille/1024/1024:.1f} MB)")
    
    # Test de lecture des premières lignes
    try:
        with open(dataset_path, 'r', encoding='utf-8') as f:
            # Lire les 1000 premiers caractères
            debut = f.read(1000)
            print(f"📄 Début du fichier :")
            print(debut[:500] + "..." if len(debut) > 500 else debut)
    except Exception as e:
        print(f"❌ Erreur lecture : {e}")
        return False
    
    # Test de parsing JSON
    try:
        print(f"\n🔄 Test de parsing JSON...")
        with open(dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ JSON parsé avec succès")
        print(f"📊 Structure :")
        print(f"   • Type : {type(data)}")
        print(f"   • Clés principales : {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
        
        if 'parties' in data:
            nb_parties = len(data['parties'])
            print(f"   • Nombre de parties : {nb_parties}")
            
            # Analyser la première partie
            if nb_parties > 0:
                partie1 = data['parties'][0]
                print(f"   • Structure partie 1 : {list(partie1.keys())}")
                
                if 'mains' in partie1:
                    nb_mains = len(partie1['mains'])
                    print(f"   • Nombre de mains partie 1 : {nb_mains}")
                    
                    # Analyser les premières mains
                    mains_valides = 0
                    mains_null = 0
                    echantillon_valides = []
                    
                    for i, main in enumerate(partie1['mains'][:20]):  # 20 premières mains
                        if main.get('main_number') is None:
                            mains_null += 1
                        else:
                            mains_valides += 1
                            if len(echantillon_valides) < 3:
                                echantillon_valides.append(main)
                    
                    print(f"   • Mains valides (20 premières) : {mains_valides}")
                    print(f"   • Mains null (20 premières) : {mains_null}")
                    
                    # Afficher échantillon de mains valides
                    for i, main in enumerate(echantillon_valides):
                        print(f"   • Main valide {i+1} :")
                        for key in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
                            if key in main:
                                print(f"     - {key}: {main[key]} ({type(main[key])})")
                            else:
                                print(f"     - {key}: ❌ MANQUANT")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur parsing JSON : {e}")
        return False

def diagnostic_analyseur():
    """Test rapide de l'analyseur"""
    print(f"\n🔍 DIAGNOSTIC DE L'ANALYSEUR")
    print("=" * 50)
    
    try:
        from analyseur_scientifique_revolutionnaire import AnalyseurScientifiqueRevolutionnaire
        
        # Test avec 1 seule partie
        analyseur = AnalyseurScientifiqueRevolutionnaire(
            "dataset_baccarat_lupasco_20250702_000230.json", 
            nb_parties_analyse=1
        )
        
        print(f"🔄 Test chargement...")
        success = analyseur.charger_dataset()
        
        if not success:
            print(f"❌ Échec chargement")
            return False
        
        print(f"✅ Chargement réussi")
        
        print(f"🔄 Test extraction...")
        mains_valides = analyseur.extraire_sequences()
        
        print(f"📊 Résultats extraction :")
        print(f"   • Mains valides : {mains_valides}")
        
        for key in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
            if key in analyseur.sequences:
                seq = analyseur.sequences[key]
                print(f"   • {key}: {len(seq)} éléments")
                if len(seq) > 0:
                    print(f"     → Type : {type(seq[0])}")
                    print(f"     → Échantillon : {seq[:5]}")
                    if key == 'index1':
                        unique_vals = np.unique(seq)
                        print(f"     → Valeurs uniques : {unique_vals}")
            else:
                print(f"   • {key}: ❌ MANQUANT")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur analyseur : {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚨 DIAGNOSTIC CRITIQUE COMPLET")
    print("=" * 60)
    
    # 1. Diagnostic du dataset
    dataset_ok = diagnostic_dataset()
    
    # 2. Diagnostic de l'analyseur
    if dataset_ok:
        analyseur_ok = diagnostic_analyseur()
    
    print(f"\n🎯 RÉSUMÉ DIAGNOSTIC :")
    print(f"   • Dataset : {'✅ OK' if dataset_ok else '❌ ERREUR'}")
    print(f"   • Analyseur : {'✅ OK' if 'analyseur_ok' in locals() and analyseur_ok else '❌ ERREUR'}")
