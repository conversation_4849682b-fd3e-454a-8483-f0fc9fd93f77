#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSE DÉTAILLÉE DES PATTERNS D'ORDRE 3 INDEX5
===============================================

Comprendre exactement pourquoi :
• Patterns déterministes (≥90%) : 0
• Patterns probabilistes (≥60%) : 0
• Taux de prédictibilité : 0.000

Malgré une information mutuelle significative de 0.937806 bits
"""

import numpy as np
import json
import os
import gc
from collections import defaultdict, Counter

# IMPORTATION DES MÊMES OPTIMISATIONS QUE L'ANALYSEUR PRINCIPAL
try:
    import msgspec
    MSGSPEC_AVAILABLE = True
    print("🚀 msgspec disponible - Performances révolutionnaires activées")
except ImportError:
    MSGSPEC_AVAILABLE = False
    print("⚠️  msgspec non disponible - Utilisation du JSON standard")

try:
    import ijson
    HAS_IJSON = True
    print("🚀 ijson disponible - Streaming JSON activé")
except ImportError:
    HAS_IJSON = False
    print("⚠️  ijson non disponible - Chargement standard")

try:
    import orjson
    HAS_ORJSON = True
    print("🚀 orjson disponible - Parsing JSON ultra-rapide")
except ImportError:
    HAS_ORJSON = False
    print("⚠️  orjson non disponible - JSON standard")

def charger_dataset_optimise(dataset_path: str, nb_parties_analyse: int = 100000):
    """
    Chargement optimisé utilisant les mêmes techniques que l'analyseur principal
    """
    print("📊 CHARGEMENT OPTIMISÉ DU DATASET...")

    # Détection de la taille du fichier
    file_size_mb = os.path.getsize(dataset_path) / (1024 * 1024)
    print(f"📊 Taille fichier : {file_size_mb:.1f} MB")

    # Stratégie de chargement selon la taille
    if file_size_mb > 1000:  # > 1GB
        print("🌊 Fichier volumineux détecté - Mode streaming forcé")
        if HAS_IJSON:
            return charger_dataset_streaming(dataset_path, nb_parties_analyse)
        else:
            return charger_dataset_standard(dataset_path, nb_parties_analyse)
    elif MSGSPEC_AVAILABLE and file_size_mb < 500:  # msgspec pour fichiers < 500MB
        return charger_dataset_msgspec(dataset_path, nb_parties_analyse)
    else:
        return charger_dataset_standard(dataset_path, nb_parties_analyse)

def charger_dataset_standard(dataset_path: str, nb_parties_analyse: int):
    """Chargement standard optimisé avec orjson si disponible"""
    try:
        print(f"📂 Chargement dataset massif (JSON optimisé)...")

        # Utiliser orjson si disponible pour parsing ultra-rapide
        if HAS_ORJSON:
            with open(dataset_path, 'rb') as f:
                data = orjson.loads(f.read())
            print("🚀 Parsing orjson ultra-rapide utilisé")
        else:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print("📂 Parsing JSON standard utilisé")

        total_parties = len(data.get('parties', data))
        print(f"✅ Dataset chargé : {total_parties} parties disponibles")
        print(f"🎯 Sélection : {min(nb_parties_analyse, total_parties)} premières parties pour analyse")

        return data

    except Exception as e:
        print(f"❌ Erreur chargement standard : {e}")
        return None

def charger_dataset_streaming(dataset_path: str, nb_parties_analyse: int):
    """Chargement par streaming avec ijson"""
    try:
        print(f"🌊 STREAMING RÉVOLUTIONNAIRE avec ijson")
        print(f"📦 Traitement par chunks optimisés")

        parties_data = []
        parties_processed = 0

        with open(dataset_path, 'rb') as f:
            # Utiliser ijson.items pour extraire directement les parties
            parties_stream = ijson.items(f, 'parties.item')

            for partie in parties_stream:
                parties_data.append(partie)
                parties_processed += 1

                # Arrêter si limite atteinte
                if parties_processed >= nb_parties_analyse:
                    break

                # Affichage du progrès
                if parties_processed % 10000 == 0:
                    print(f"   📊 Parties traitées : {parties_processed:,}")

        print(f"✅ Streaming terminé : {parties_processed:,} parties chargées")

        # Retourner dans le format attendu
        return {'parties': parties_data}

    except Exception as e:
        print(f"❌ Erreur streaming : {e}")
        return charger_dataset_standard(dataset_path, nb_parties_analyse)

def charger_dataset_msgspec(dataset_path: str, nb_parties_analyse: int):
    """Chargement ultra-optimisé avec msgspec"""
    try:
        print(f"🚀 Chargement dataset massif avec msgspec...")

        with open(dataset_path, 'rb') as f:
            data_raw = f.read()
            print(f"📊 Taille fichier : {len(data_raw) / (1024*1024):.1f} MB")

        # Parsing ultra-rapide
        data = msgspec.json.decode(data_raw)
        print("✅ Parsing msgspec réussi")

        return data

    except Exception as e:
        print(f"❌ Erreur msgspec : {e}")
        return charger_dataset_standard(dataset_path, nb_parties_analyse)

def extraire_sequence_index5(data, nb_parties_analyse: int):
    """Extraction optimisée de la séquence INDEX5"""
    print("🔄 Extraction de la séquence INDEX5...")

    seq_index5 = []
    parties_traitees = 0

    # Gestion des différents formats de données
    parties = data.get('parties', data) if isinstance(data, dict) else data

    for partie in parties:
        if parties_traitees >= nb_parties_analyse:
            break

        if 'mains' in partie:
            for main in partie['mains']:
                if 'index5' in main and main['index5'] is not None:
                    seq_index5.append(main['index5'])

        parties_traitees += 1

        # Affichage du progrès
        if parties_traitees % 10000 == 0:
            print(f"   📊 Parties traitées : {parties_traitees:,}, INDEX5 extraits : {len(seq_index5):,}")

    print(f"✅ Extraction terminée : {len(seq_index5):,} éléments INDEX5")
    return np.array(seq_index5)

def analyser_patterns_ordre3_detaille():
    """
    Analyse détaillée des patterns d'ordre 3 pour comprendre
    la distribution des probabilités conditionnelles
    """
    print("🔍 ANALYSE DÉTAILLÉE DES PATTERNS D'ORDRE 3 INDEX5")
    print("=" * 60)

    # Chargement optimisé des données
    dataset_path = "dataset_baccarat_lupasco_20250702_000230.json"
    nb_parties_analyse = 100000

    try:
        # Chargement avec les mêmes techniques que l'analyseur principal
        data = charger_dataset_optimise(dataset_path, nb_parties_analyse)

        if data is None:
            print("❌ Échec du chargement des données")
            return

        # Extraction de la séquence INDEX5
        seq_index5 = extraire_sequence_index5(data, nb_parties_analyse)
        n = len(seq_index5)

        if n < 3:
            print(f"❌ Erreur : Séquence trop courte ({n} éléments, minimum 3 requis)")
            return

        # Libération mémoire
        del data
        gc.collect()

    except Exception as e:
        print(f"❌ Erreur chargement : {e}")
        import traceback
        print(f"   Détails : {traceback.format_exc()}")
        return
    
    print(f"✅ Séquence INDEX5 chargée : {n:,} éléments")
    
    # 1. CONSTRUCTION DES TRIPLETS ET PROBABILITÉS
    print("\n🔬 CONSTRUCTION DES MATRICES DE TRANSITION...")
    
    triplets_transitions = defaultdict(lambda: defaultdict(int))
    
    for i in range(n - 2):
        v1 = seq_index5[i]
        v2 = seq_index5[i + 1]
        v3 = seq_index5[i + 2]
        
        cle_contexte = (v1, v2)
        triplets_transitions[cle_contexte][v3] += 1
    
    # 2. CALCUL DES PROBABILITÉS CONDITIONNELLES
    probabilites_conditionnelles = {}
    probabilites_max = []
    
    for contexte, transitions in triplets_transitions.items():
        total_transitions = sum(transitions.values())
        if total_transitions > 0:
            probs = {v3: count/total_transitions for v3, count in transitions.items()}
            probabilites_conditionnelles[contexte] = probs
            
            # Probabilité maximale pour ce contexte
            prob_max = max(probs.values())
            probabilites_max.append(prob_max)
    
    # 3. ANALYSE STATISTIQUE DES PROBABILITÉS MAXIMALES
    probabilites_max = np.array(probabilites_max)
    
    print(f"\n📊 STATISTIQUES DES PROBABILITÉS MAXIMALES :")
    print(f"   • Nombre de contextes : {len(probabilites_max)}")
    print(f"   • Probabilité max moyenne : {np.mean(probabilites_max):.6f}")
    print(f"   • Probabilité max médiane : {np.median(probabilites_max):.6f}")
    print(f"   • Probabilité max min : {np.min(probabilites_max):.6f}")
    print(f"   • Probabilité max max : {np.max(probabilites_max):.6f}")
    print(f"   • Écart-type : {np.std(probabilites_max):.6f}")
    
    # 4. DISTRIBUTION PAR SEUILS
    seuils = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3]
    
    print(f"\n🎯 DISTRIBUTION PAR SEUILS DE PRÉDICTIBILITÉ :")
    for seuil in seuils:
        nb_au_dessus = np.sum(probabilites_max >= seuil)
        pourcentage = (nb_au_dessus / len(probabilites_max)) * 100
        print(f"   • ≥ {seuil:.1f} : {nb_au_dessus:3d} contextes ({pourcentage:5.2f}%)")
    
    # 5. ANALYSE DES CONTEXTES LES PLUS PRÉDICTIBLES
    print(f"\n🔥 TOP 10 CONTEXTES LES PLUS PRÉDICTIBLES :")
    
    contextes_tries = []
    for contexte, probs in probabilites_conditionnelles.items():
        prob_max = max(probs.values())
        v3_predit = max(probs.items(), key=lambda x: x[1])[0]
        total_obs = sum(triplets_transitions[contexte].values())
        
        contextes_tries.append({
            'contexte': contexte,
            'v3_predit': v3_predit,
            'prob_max': prob_max,
            'total_obs': total_obs,
            'distribution': probs
        })
    
    # Trier par probabilité maximale décroissante
    contextes_tries.sort(key=lambda x: x['prob_max'], reverse=True)
    
    for i, ctx in enumerate(contextes_tries[:10]):
        v1, v2 = ctx['contexte']
        print(f"   {i+1:2d}. ({v1}, {v2}) → {ctx['v3_predit']} : {ctx['prob_max']:.4f} ({ctx['total_obs']:,} obs)")
        
        # Afficher la distribution complète pour les 3 premiers
        if i < 3:
            print(f"       Distribution complète :")
            for v3, prob in sorted(ctx['distribution'].items(), key=lambda x: x[1], reverse=True):
                print(f"         {v3} : {prob:.4f}")
    
    # 6. ANALYSE DE L'ÉQUILIBRE DU SYSTÈME
    print(f"\n⚖️  ANALYSE DE L'ÉQUILIBRE DU SYSTÈME :")
    
    # Entropie moyenne des distributions
    entropies = []
    for probs in probabilites_conditionnelles.values():
        entropie = 0
        for prob in probs.values():
            if prob > 0:
                entropie -= prob * np.log2(prob)
        entropies.append(entropie)
    
    entropie_moyenne = np.mean(entropies)
    entropie_max_theorique = np.log2(18)  # 18 valeurs INDEX5 possibles
    
    print(f"   • Entropie moyenne des contextes : {entropie_moyenne:.6f} bits")
    print(f"   • Entropie maximale théorique : {entropie_max_theorique:.6f} bits")
    print(f"   • Ratio d'équilibre : {entropie_moyenne/entropie_max_theorique:.4f}")
    
    # 7. EXPLICATION DU PARADOXE
    print(f"\n🧠 EXPLICATION DU PARADOXE RÉVOLUTIONNAIRE :")
    print(f"   • Information mutuelle élevée (0.937806 bits) MAIS")
    print(f"   • Aucun pattern déterministe/probabiliste")
    print(f"   • Cela révèle un ÉQUILIBRE SOPHISTIQUÉ :")
    print(f"     - Le système a de la MÉMOIRE (information mutuelle)")
    print(f"     - Mais cette mémoire est DISTRIBUÉE équitablement")
    print(f"     - Aucun contexte ne DOMINE les autres")
    print(f"     - Structure ANTI-FRAGILE et ADAPTATIVE")
    
    # 8. CALCUL DE L'EFFICACITÉ RÉELLE
    print(f"\n📈 EFFICACITÉ RÉELLE DU SYSTÈME :")
    
    # Entropie marginale H(V3)
    count_v3 = Counter(seq_index5[2:])
    total_v3 = sum(count_v3.values())
    entropie_marginale = 0
    for count in count_v3.values():
        p = count / total_v3
        if p > 0:
            entropie_marginale -= p * np.log2(p)
    
    # Entropie conditionnelle moyenne
    entropie_conditionnelle_moyenne = np.mean(entropies)
    
    # Information mutuelle
    information_mutuelle = entropie_marginale - entropie_conditionnelle_moyenne
    efficacite = information_mutuelle / entropie_marginale
    
    print(f"   • H(V3) marginale : {entropie_marginale:.6f} bits")
    print(f"   • H(V3|V1,V2) moyenne : {entropie_conditionnelle_moyenne:.6f} bits")
    print(f"   • I(V3;V1,V2) : {information_mutuelle:.6f} bits")
    print(f"   • Efficacité de prédiction : {efficacite:.4f} ({efficacite*100:.2f}%)")
    
    print(f"\n✅ ANALYSE TERMINÉE")
    
    return {
        'probabilites_max': probabilites_max,
        'contextes_tries': contextes_tries,
        'information_mutuelle': information_mutuelle,
        'efficacite': efficacite
    }

if __name__ == "__main__":
    resultats = analyser_patterns_ordre3_detaille()
