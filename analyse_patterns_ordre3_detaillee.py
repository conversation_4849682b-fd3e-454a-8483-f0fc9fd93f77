#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSE DÉTAILLÉE DES PATTERNS D'ORDRE 3 INDEX5
===============================================

Comprendre exactement pourquoi :
• Patterns déterministes (≥90%) : 0
• Patterns probabilistes (≥60%) : 0
• Taux de prédictibilité : 0.000

Malgré une information mutuelle significative de 0.937806 bits
"""

import numpy as np
import json
from collections import defaultdict, Counter
from analyseur_scientifique_revolutionnaire import AnalyseurScientifiqueRevolutionnaire

def analyser_patterns_ordre3_detaille():
    """
    Analyse détaillée des patterns d'ordre 3 pour comprendre
    la distribution des probabilités conditionnelles
    """
    print("🔍 ANALYSE DÉTAILLÉE DES PATTERNS D'ORDRE 3 INDEX5")
    print("=" * 60)
    
    # Chargement des données via l'analyseur SANS analyse automatique
    print("📊 Chargement des données...")

    try:
        # Chargement direct des données JSON pour éviter les problèmes d'initialisation
        print("🔄 Chargement direct du fichier JSON...")
        with open("dataset_baccarat_lupasco_20250702_000230.json", 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"✅ Données chargées : {len(data)} parties")

        # Extraction directe de la séquence INDEX5
        print("🔄 Extraction de la séquence INDEX5...")
        seq_index5 = []

        for partie in data:
            if 'mains' in partie:
                for main in partie['mains']:
                    if 'index5' in main and main['index5'] is not None:
                        seq_index5.append(main['index5'])

        seq_index5 = np.array(seq_index5)
        n = len(seq_index5)

        if n < 3:
            print(f"❌ Erreur : Séquence trop courte ({n} éléments, minimum 3 requis)")
            return

    except Exception as e:
        print(f"❌ Erreur chargement : {e}")
        print(f"   Type d'erreur : {type(e).__name__}")
        import traceback
        print(f"   Détails : {traceback.format_exc()}")
        return
    
    print(f"✅ Séquence INDEX5 chargée : {n:,} éléments")
    
    # 1. CONSTRUCTION DES TRIPLETS ET PROBABILITÉS
    print("\n🔬 CONSTRUCTION DES MATRICES DE TRANSITION...")
    
    triplets_transitions = defaultdict(lambda: defaultdict(int))
    
    for i in range(n - 2):
        v1 = seq_index5[i]
        v2 = seq_index5[i + 1]
        v3 = seq_index5[i + 2]
        
        cle_contexte = (v1, v2)
        triplets_transitions[cle_contexte][v3] += 1
    
    # 2. CALCUL DES PROBABILITÉS CONDITIONNELLES
    probabilites_conditionnelles = {}
    probabilites_max = []
    
    for contexte, transitions in triplets_transitions.items():
        total_transitions = sum(transitions.values())
        if total_transitions > 0:
            probs = {v3: count/total_transitions for v3, count in transitions.items()}
            probabilites_conditionnelles[contexte] = probs
            
            # Probabilité maximale pour ce contexte
            prob_max = max(probs.values())
            probabilites_max.append(prob_max)
    
    # 3. ANALYSE STATISTIQUE DES PROBABILITÉS MAXIMALES
    probabilites_max = np.array(probabilites_max)
    
    print(f"\n📊 STATISTIQUES DES PROBABILITÉS MAXIMALES :")
    print(f"   • Nombre de contextes : {len(probabilites_max)}")
    print(f"   • Probabilité max moyenne : {np.mean(probabilites_max):.6f}")
    print(f"   • Probabilité max médiane : {np.median(probabilites_max):.6f}")
    print(f"   • Probabilité max min : {np.min(probabilites_max):.6f}")
    print(f"   • Probabilité max max : {np.max(probabilites_max):.6f}")
    print(f"   • Écart-type : {np.std(probabilites_max):.6f}")
    
    # 4. DISTRIBUTION PAR SEUILS
    seuils = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3]
    
    print(f"\n🎯 DISTRIBUTION PAR SEUILS DE PRÉDICTIBILITÉ :")
    for seuil in seuils:
        nb_au_dessus = np.sum(probabilites_max >= seuil)
        pourcentage = (nb_au_dessus / len(probabilites_max)) * 100
        print(f"   • ≥ {seuil:.1f} : {nb_au_dessus:3d} contextes ({pourcentage:5.2f}%)")
    
    # 5. ANALYSE DES CONTEXTES LES PLUS PRÉDICTIBLES
    print(f"\n🔥 TOP 10 CONTEXTES LES PLUS PRÉDICTIBLES :")
    
    contextes_tries = []
    for contexte, probs in probabilites_conditionnelles.items():
        prob_max = max(probs.values())
        v3_predit = max(probs.items(), key=lambda x: x[1])[0]
        total_obs = sum(triplets_transitions[contexte].values())
        
        contextes_tries.append({
            'contexte': contexte,
            'v3_predit': v3_predit,
            'prob_max': prob_max,
            'total_obs': total_obs,
            'distribution': probs
        })
    
    # Trier par probabilité maximale décroissante
    contextes_tries.sort(key=lambda x: x['prob_max'], reverse=True)
    
    for i, ctx in enumerate(contextes_tries[:10]):
        v1, v2 = ctx['contexte']
        print(f"   {i+1:2d}. ({v1}, {v2}) → {ctx['v3_predit']} : {ctx['prob_max']:.4f} ({ctx['total_obs']:,} obs)")
        
        # Afficher la distribution complète pour les 3 premiers
        if i < 3:
            print(f"       Distribution complète :")
            for v3, prob in sorted(ctx['distribution'].items(), key=lambda x: x[1], reverse=True):
                print(f"         {v3} : {prob:.4f}")
    
    # 6. ANALYSE DE L'ÉQUILIBRE DU SYSTÈME
    print(f"\n⚖️  ANALYSE DE L'ÉQUILIBRE DU SYSTÈME :")
    
    # Entropie moyenne des distributions
    entropies = []
    for probs in probabilites_conditionnelles.values():
        entropie = 0
        for prob in probs.values():
            if prob > 0:
                entropie -= prob * np.log2(prob)
        entropies.append(entropie)
    
    entropie_moyenne = np.mean(entropies)
    entropie_max_theorique = np.log2(18)  # 18 valeurs INDEX5 possibles
    
    print(f"   • Entropie moyenne des contextes : {entropie_moyenne:.6f} bits")
    print(f"   • Entropie maximale théorique : {entropie_max_theorique:.6f} bits")
    print(f"   • Ratio d'équilibre : {entropie_moyenne/entropie_max_theorique:.4f}")
    
    # 7. EXPLICATION DU PARADOXE
    print(f"\n🧠 EXPLICATION DU PARADOXE RÉVOLUTIONNAIRE :")
    print(f"   • Information mutuelle élevée (0.937806 bits) MAIS")
    print(f"   • Aucun pattern déterministe/probabiliste")
    print(f"   • Cela révèle un ÉQUILIBRE SOPHISTIQUÉ :")
    print(f"     - Le système a de la MÉMOIRE (information mutuelle)")
    print(f"     - Mais cette mémoire est DISTRIBUÉE équitablement")
    print(f"     - Aucun contexte ne DOMINE les autres")
    print(f"     - Structure ANTI-FRAGILE et ADAPTATIVE")
    
    # 8. CALCUL DE L'EFFICACITÉ RÉELLE
    print(f"\n📈 EFFICACITÉ RÉELLE DU SYSTÈME :")
    
    # Entropie marginale H(V3)
    count_v3 = Counter(seq_index5[2:])
    total_v3 = sum(count_v3.values())
    entropie_marginale = 0
    for count in count_v3.values():
        p = count / total_v3
        if p > 0:
            entropie_marginale -= p * np.log2(p)
    
    # Entropie conditionnelle moyenne
    entropie_conditionnelle_moyenne = np.mean(entropies)
    
    # Information mutuelle
    information_mutuelle = entropie_marginale - entropie_conditionnelle_moyenne
    efficacite = information_mutuelle / entropie_marginale
    
    print(f"   • H(V3) marginale : {entropie_marginale:.6f} bits")
    print(f"   • H(V3|V1,V2) moyenne : {entropie_conditionnelle_moyenne:.6f} bits")
    print(f"   • I(V3;V1,V2) : {information_mutuelle:.6f} bits")
    print(f"   • Efficacité de prédiction : {efficacite:.4f} ({efficacite*100:.2f}%)")
    
    print(f"\n✅ ANALYSE TERMINÉE")
    
    return {
        'probabilites_max': probabilites_max,
        'contextes_tries': contextes_tries,
        'information_mutuelle': information_mutuelle,
        'efficacite': efficacite
    }

if __name__ == "__main__":
    resultats = analyser_patterns_ordre3_detaille()
