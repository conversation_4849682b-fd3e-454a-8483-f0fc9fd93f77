#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DIAGNOSTIC CRITIQUE DU MAPPING ANALYSEUR SCIENTIFIQUE RÉVOLUTIONNAIRE
====================================================================

Diagnostic complet pour identifier toutes les incohérences de mapping
dans l'analyseur_scientifique_revolutionnaire.py
"""

import re
import ast

def analyser_mapping_analyseur():
    """Analyse complète du mapping dans l'analyseur"""
    print("🚨 DIAGNOSTIC CRITIQUE DU MAPPING ANALYSEUR")
    print("=" * 70)
    
    # Lire le fichier analyseur
    with open('analyseur_scientifique_revolutionnaire.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    # Mapping attendu selon les spécifications
    mapping_attendu = {
        'index1': [0, 1],
        'index2': ['A', 'B', 'C'],
        'index3': ['BANKER', 'PLAYER', 'TIE'],
        'index5': [
            '0_A_BANKER', '1_A_BANKER', '0_B_BANKER', '1_B_BANKER', '0_C_BANKER', '1_C_BANKER',
            '0_A_PLAYER', '1_A_PLAYER', '0_B_PLAYER', '1_B_PLAYER', '0_C_PLAYER', '1_C_PLAYER',
            '0_A_TIE', '1_A_TIE', '0_B_TIE', '1_B_TIE', '0_C_TIE', '1_C_TIE'
        ]
    }
    
    print("📋 MAPPING ATTENDU :")
    for key, values in mapping_attendu.items():
        print(f"   • {key}: {values}")
    
    print(f"\n🔍 RECHERCHE D'INCOHÉRENCES DANS LE CODE...")
    
    # 1. Rechercher les références aux valeurs INDEX
    incohérences = []
    
    # Patterns problématiques à rechercher
    patterns_problematiques = [
        # INDEX1 incorrect
        (r"== 'SYNC'", "INDEX1 utilise 'SYNC' au lieu de 0"),
        (r"== 'DESYNC'", "INDEX1 utilise 'DESYNC' au lieu de 1"),
        (r"'SYNC'", "Référence à 'SYNC' au lieu de 0"),
        (r"'DESYNC'", "Référence à 'DESYNC' au lieu de 1"),
        
        # INDEX2 incorrect
        (r"== 'NATUREL'", "INDEX2 utilise 'NATUREL' au lieu de 'A'"),
        (r"== 'DOUBLE'", "INDEX2 utilise 'DOUBLE' au lieu de 'B'"),
        (r"== 'SIMPLE'", "INDEX2 utilise 'SIMPLE' au lieu de 'C'"),
        
        # INDEX3 incorrect
        (r"== 'BANK'", "INDEX3 utilise 'BANK' au lieu de 'BANKER'"),
        (r"== 'PLAY'", "INDEX3 utilise 'PLAY' au lieu de 'PLAYER'"),
        
        # INDEX5 format incorrect
        (r"SYNC_", "INDEX5 utilise 'SYNC_' au lieu de '0_'"),
        (r"DESYNC_", "INDEX5 utilise 'DESYNC_' au lieu de '1_'"),
        (r"_NATUREL", "INDEX5 utilise '_NATUREL' au lieu de '_A'"),
        (r"_DOUBLE", "INDEX5 utilise '_DOUBLE' au lieu de '_B'"),
        (r"_SIMPLE", "INDEX5 utilise '_SIMPLE' au lieu de '_C'"),
    ]
    
    # Rechercher les patterns problématiques
    for pattern, description in patterns_problematiques:
        matches = re.finditer(pattern, code, re.IGNORECASE)
        for match in matches:
            # Trouver le numéro de ligne
            ligne_num = code[:match.start()].count('\n') + 1
            ligne_contexte = code.split('\n')[ligne_num - 1].strip()
            incohérences.append({
                'ligne': ligne_num,
                'pattern': pattern,
                'description': description,
                'contexte': ligne_contexte
            })
    
    # 2. Rechercher les mappings hardcodés incorrects
    print(f"\n🔍 RECHERCHE DE MAPPINGS HARDCODÉS...")
    
    # Rechercher les dictionnaires de mapping
    dict_patterns = [
        r"mapping_\w+\s*=\s*{[^}]+}",
        r"\w+_mapping\s*=\s*{[^}]+}",
        r"{\s*['\"]SYNC['\"]",
        r"{\s*['\"]DESYNC['\"]",
    ]
    
    for pattern in dict_patterns:
        matches = re.finditer(pattern, code, re.MULTILINE | re.DOTALL)
        for match in matches:
            ligne_num = code[:match.start()].count('\n') + 1
            contexte = match.group(0)[:100] + "..." if len(match.group(0)) > 100 else match.group(0)
            incohérences.append({
                'ligne': ligne_num,
                'pattern': pattern,
                'description': "Mapping hardcodé potentiellement incorrect",
                'contexte': contexte
            })
    
    # 3. Afficher les incohérences trouvées
    print(f"\n🚨 INCOHÉRENCES DÉTECTÉES : {len(incohérences)}")
    print("=" * 70)
    
    if incohérences:
        for i, inc in enumerate(incohérences, 1):
            print(f"\n❌ INCOHÉRENCE {i} :")
            print(f"   • Ligne {inc['ligne']}")
            print(f"   • Pattern : {inc['pattern']}")
            print(f"   • Description : {inc['description']}")
            print(f"   • Contexte : {inc['contexte']}")
    else:
        print("✅ Aucune incohérence évidente détectée dans les patterns de base")
    
    # 4. Rechercher les fonctions d'analyse spécifiques
    print(f"\n🔍 ANALYSE DES FONCTIONS D'ANALYSE...")
    
    fonctions_analyse = [
        'analyser_equilibre_sync_desync',
        'analyser_correlations_sequentielles',
        'analyser_mecanismes_compensation',
        'analyser_structure_fractale',
        'analyser_entropie_controlee'
    ]
    
    for fonction in fonctions_analyse:
        pattern = rf"def {fonction}\(.*?\):(.*?)(?=def|\Z)"
        match = re.search(pattern, code, re.DOTALL)
        if match:
            fonction_code = match.group(1)
            ligne_debut = code[:match.start()].count('\n') + 1
            
            print(f"\n📊 FONCTION : {fonction} (ligne {ligne_debut})")
            
            # Rechercher les références aux index dans cette fonction
            for index_name, expected_values in mapping_attendu.items():
                if index_name in fonction_code:
                    print(f"   • Utilise {index_name}")
                    
                    # Vérifier les valeurs utilisées
                    for value in expected_values:
                        if isinstance(value, str):
                            if f"'{value}'" in fonction_code or f'"{value}"' in fonction_code:
                                print(f"     → Référence correcte à '{value}'")
                        else:
                            if f"== {value}" in fonction_code:
                                print(f"     → Référence correcte à {value}")
    
    return incohérences

def generer_corrections():
    """Génère les corrections nécessaires"""
    print(f"\n🔧 CORRECTIONS RECOMMANDÉES :")
    print("=" * 50)
    
    corrections = [
        "1. Remplacer toutes les références 'SYNC' par 0",
        "2. Remplacer toutes les références 'DESYNC' par 1", 
        "3. Vérifier que INDEX2 utilise 'A', 'B', 'C'",
        "4. Vérifier que INDEX3 utilise 'BANKER', 'PLAYER', 'TIE'",
        "5. Vérifier le format INDEX5 : '0_A_BANKER', '1_B_PLAYER', etc.",
        "6. Corriger les mappings hardcodés dans les fonctions d'analyse",
        "7. Mettre à jour la validation des données extraites",
        "8. Corriger les clés de stockage des résultats"
    ]
    
    for correction in corrections:
        print(f"   • {correction}")

if __name__ == "__main__":
    incohérences = analyser_mapping_analyseur()
    generer_corrections()
    
    print(f"\n🎯 RÉSUMÉ :")
    print(f"   • Incohérences détectées : {len(incohérences)}")
    print(f"   • Mapping attendu : INDEX1=[0,1], INDEX2=[A,B,C], INDEX3=[BANKER,PLAYER,TIE]")
    print(f"   • INDEX5 : 18 valeurs combinées (ex: '0_A_BANKER', '1_C_TIE')")
