#!/usr/bin/env python3
"""
DIAGNOSTIC APPROFONDI : Information Mutuelle Nulle
Analyse pourquoi I(INDEX1;INDEX2) = 0.000000 et I(INDEX1;INDEX3) = 0.000000
"""

import numpy as np
from collections import Counter
import pandas as pd

def analyser_information_mutuelle_detaillee():
    """Diagnostic approfondi de l'information mutuelle avec données simulées"""

    print("🔬 DIAGNOSTIC APPROFONDI : Information Mutuelle")
    print("=" * 60)

    # Simulation de données réalistes basées sur les observations
    print("📊 Simulation de données réalistes...")

    n_samples = 10000

    # INDEX1: SYNC/DESYNC avec distribution observée (49.7% / 50.3%)
    index1_seq = np.random.choice([0, 1], size=n_samples, p=[0.497, 0.503])

    # INDEX2: A/B/C avec distribution observée (37.9% / 31.8% / 30.3%)
    index2_seq = np.random.choice(['A', 'B', 'C'], size=n_samples, p=[0.379, 0.318, 0.303])

    # INDEX3: BANKER/PLAYER/TIE avec distribution réaliste
    index3_seq = np.random.choice(['BANKER', 'PLAYER', 'TIE'], size=n_samples, p=[0.458, 0.446, 0.096])

    print(f"✅ {len(index1_seq)} mains simulées pour diagnostic")
    
    # Conversion en arrays
    seq1 = np.array(index1_seq)
    seq2 = np.array(index2_seq)
    seq3 = np.array(index3_seq)
    
    print(f"\n🔍 DIAGNOSTIC DES SÉQUENCES :")
    print(f"   • INDEX1 : {len(np.unique(seq1))} valeurs uniques : {np.unique(seq1)}")
    print(f"   • INDEX2 : {len(np.unique(seq2))} valeurs uniques : {np.unique(seq2)}")
    print(f"   • INDEX3 : {len(np.unique(seq3))} valeurs uniques : {np.unique(seq3)}")
    
    # Mapping pour INDEX2 et INDEX3
    map2 = {val: idx for idx, val in enumerate(np.unique(seq2))}
    map3 = {val: idx for idx, val in enumerate(np.unique(seq3))}
    
    seq2_mapped = np.array([map2[x] for x in seq2])
    seq3_mapped = np.array([map3[x] for x in seq3])
    
    print(f"\n🔧 APRÈS MAPPING :")
    print(f"   • INDEX1 : min={seq1.min()}, max={seq1.max()}")
    print(f"   • INDEX2 : min={seq2_mapped.min()}, max={seq2_mapped.max()}")
    print(f"   • INDEX3 : min={seq3_mapped.min()}, max={seq3_mapped.max()}")
    
    # Calcul des entropies manuellement
    def calcul_entropie_manuelle(sequence):
        counts = Counter(sequence)
        total = len(sequence)
        entropie = 0
        for count in counts.values():
            p = count / total
            if p > 0:
                entropie -= p * np.log2(p)
        return entropie
    
    def calcul_entropie_jointe_manuelle(seq_a, seq_b):
        pairs = list(zip(seq_a, seq_b))
        counts = Counter(pairs)
        total = len(pairs)
        entropie = 0
        for count in counts.values():
            p = count / total
            if p > 0:
                entropie -= p * np.log2(p)
        return entropie
    
    # Calculs d'entropie
    H1 = calcul_entropie_manuelle(seq1)
    H2 = calcul_entropie_manuelle(seq2_mapped)
    H3 = calcul_entropie_manuelle(seq3_mapped)
    
    H12 = calcul_entropie_jointe_manuelle(seq1, seq2_mapped)
    H13 = calcul_entropie_jointe_manuelle(seq1, seq3_mapped)
    H23 = calcul_entropie_jointe_manuelle(seq2_mapped, seq3_mapped)
    
    # Informations mutuelles
    I_12 = H1 + H2 - H12
    I_13 = H1 + H3 - H13
    I_23 = H2 + H3 - H23
    
    print(f"\n📊 ENTROPIES CALCULÉES :")
    print(f"   • H(INDEX1) = {H1:.6f} bits")
    print(f"   • H(INDEX2) = {H2:.6f} bits")
    print(f"   • H(INDEX3) = {H3:.6f} bits")
    print(f"   • H(INDEX1,INDEX2) = {H12:.6f} bits")
    print(f"   • H(INDEX1,INDEX3) = {H13:.6f} bits")
    print(f"   • H(INDEX2,INDEX3) = {H23:.6f} bits")
    
    print(f"\n🔬 INFORMATIONS MUTUELLES :")
    print(f"   • I(INDEX1;INDEX2) = {H1:.6f} + {H2:.6f} - {H12:.6f} = {I_12:.6f} bits")
    print(f"   • I(INDEX1;INDEX3) = {H1:.6f} + {H3:.6f} - {H13:.6f} = {I_13:.6f} bits")
    print(f"   • I(INDEX2;INDEX3) = {H2:.6f} + {H3:.6f} - {H23:.6f} = {I_23:.6f} bits")
    
    # Test avec sklearn pour comparaison
    print(f"\n🔬 COMPARAISON AVEC SKLEARN :")
    mi_12_sklearn = mutual_info_score(seq1, seq2_mapped)
    mi_13_sklearn = mutual_info_score(seq1, seq3_mapped)
    mi_23_sklearn = mutual_info_score(seq2_mapped, seq3_mapped)
    
    print(f"   • sklearn I(1;2) = {mi_12_sklearn:.6f} bits")
    print(f"   • sklearn I(1;3) = {mi_13_sklearn:.6f} bits")
    print(f"   • sklearn I(2;3) = {mi_23_sklearn:.6f} bits")
    
    # Analyse des tables de contingence
    print(f"\n📊 TABLES DE CONTINGENCE :")
    
    # Table INDEX1 x INDEX2
    df_12 = pd.crosstab(seq1, seq2_mapped, margins=True)
    print(f"\n   INDEX1 x INDEX2 :")
    print(df_12)
    
    # Table INDEX1 x INDEX3
    df_13 = pd.crosstab(seq1, seq3_mapped, margins=True)
    print(f"\n   INDEX1 x INDEX3 :")
    print(df_13)
    
    # Analyse de l'indépendance
    print(f"\n🔍 ANALYSE D'INDÉPENDANCE :")
    
    # Pour INDEX1 x INDEX2
    total = len(seq1)
    for val1 in np.unique(seq1):
        for val2 in np.unique(seq2_mapped):
            observed = np.sum((seq1 == val1) & (seq2_mapped == val2))
            expected = (np.sum(seq1 == val1) * np.sum(seq2_mapped == val2)) / total
            print(f"   INDEX1={val1}, INDEX2={val2}: observé={observed}, attendu={expected:.1f}, ratio={observed/expected:.3f}")

if __name__ == "__main__":
    analyser_information_mutuelle_detaillee()
