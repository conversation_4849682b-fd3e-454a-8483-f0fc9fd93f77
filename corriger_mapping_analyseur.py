#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CORRECTION AUTOMATIQUE DU MAPPING ANALYSEUR SCIENTIFIQUE RÉVOLUTIONNAIRE
========================================================================

Script pour corriger automatiquement toutes les incohérences de mapping
détectées dans l'analyseur_scientifique_revolutionnaire.py
"""

import re
import shutil
from datetime import datetime

def corriger_mapping_analyseur():
    """Corrige automatiquement toutes les incohérences de mapping"""
    print("🔧 CORRECTION AUTOMATIQUE DU MAPPING ANALYSEUR")
    print("=" * 70)
    
    # Backup du fichier original
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"analyseur_scientifique_revolutionnaire_backup_{timestamp}.py"
    shutil.copy2('analyseur_scientifique_revolutionnaire.py', backup_file)
    print(f"✅ Backup créé : {backup_file}")
    
    # Lire le fichier
    with open('analyseur_scientifique_revolutionnaire.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    original_code = code
    corrections_appliquees = []
    
    # 1. CORRECTION CRITIQUE : Problème principal détecté
    print(f"\n🚨 CORRECTION 1 : Noms de fonctions et clés de résultats")
    
    # Le problème principal : les noms utilisent 'sync_desync' au lieu de valeurs numériques
    # Mais les noms de fonctions et clés sont corrects, le problème est ailleurs
    
    # 2. CORRECTION : Commentaires et print statements incorrects
    print(f"\n🔧 CORRECTION 2 : Commentaires et affichages")
    
    corrections_commentaires = [
        # Corriger les commentaires qui utilisent des termes incorrects
        (r'SYNC_DOMINANT', 'SYNC_DOMINANT (état 0)', "Clarification état SYNC"),
        (r'DESYNC_DOMINANT', 'DESYNC_DOMINANT (état 2)', "Clarification état DESYNC"),
        (r'_SIMPLE', '_C', "Correction INDEX2 de SIMPLE vers C"),
    ]
    
    for pattern, replacement, description in corrections_commentaires:
        if re.search(pattern, code):
            code = re.sub(pattern, replacement, code)
            corrections_appliquees.append(f"✅ {description}")
    
    # 3. DIAGNOSTIC PLUS PROFOND : Rechercher le vrai problème
    print(f"\n🔍 DIAGNOSTIC APPROFONDI...")
    
    # Rechercher les fonctions qui utilisent les mauvaises valeurs pour les comparaisons
    problemes_detectes = []
    
    # Rechercher les comparaisons incorrectes avec des chaînes au lieu de nombres
    patterns_incorrects = [
        (r"== ['\"]SYNC['\"]", "Comparaison INDEX1 avec 'SYNC' au lieu de 0"),
        (r"== ['\"]DESYNC['\"]", "Comparaison INDEX1 avec 'DESYNC' au lieu de 1"),
        (r"== ['\"]0['\"]", "Comparaison INDEX1 avec '0' (string) au lieu de 0 (int)"),
        (r"== ['\"]1['\"]", "Comparaison INDEX1 avec '1' (string) au lieu de 1 (int)"),
    ]
    
    for pattern, description in patterns_incorrects:
        matches = list(re.finditer(pattern, code))
        if matches:
            for match in matches:
                ligne_num = code[:match.start()].count('\n') + 1
                ligne_contexte = code.split('\n')[ligne_num - 1].strip()
                problemes_detectes.append({
                    'ligne': ligne_num,
                    'description': description,
                    'contexte': ligne_contexte
                })
    
    # 4. RECHERCHER LES VRAIS PROBLÈMES DANS LES FONCTIONS D'ANALYSE
    print(f"\n🔍 RECHERCHE DES VRAIS PROBLÈMES...")
    
    # Analyser la fonction analyser_equilibre_sync_desync
    pattern_fonction = r"def analyser_equilibre_sync_desync\(.*?\):(.*?)(?=def|\Z)"
    match = re.search(pattern_fonction, code, re.DOTALL)
    
    if match:
        fonction_code = match.group(1)
        print(f"📊 Analyse de analyser_equilibre_sync_desync:")
        
        # Vérifier les comparaisons dans cette fonction
        if "== 0" in fonction_code and "== 1" in fonction_code:
            print(f"   ✅ Utilise correctement 0 et 1 pour INDEX1")
        else:
            print(f"   ❌ Problème de comparaison INDEX1 détecté")
            
        # Vérifier le stockage des résultats
        if "n_sync" in fonction_code and "n_desync" in fonction_code:
            print(f"   ✅ Variables n_sync et n_desync présentes")
        else:
            print(f"   ❌ Variables de comptage manquantes")
    
    # 5. VÉRIFIER LA FONCTION DE GÉNÉRATION DE RAPPORT
    print(f"\n🔍 VÉRIFICATION GÉNÉRATION RAPPORT...")
    
    pattern_rapport = r"def generer_rapport_revolutionnaire_complet\(.*?\):(.*?)(?=def|\Z)"
    match = re.search(pattern_rapport, code, re.DOTALL)
    
    if match:
        rapport_code = match.group(1)
        
        # Vérifier les clés utilisées dans le rapport
        cles_utilisees = []
        for cle in ['n_sync', 'n_desync', 'count_sync', 'count_desync', 'proportion_sync', 'proportion_desync']:
            if cle in rapport_code:
                cles_utilisees.append(cle)
        
        print(f"   📋 Clés utilisées dans le rapport : {cles_utilisees}")
        
        # Détecter les incohérences de clés
        if 'count_sync' in cles_utilisees and 'n_sync' not in cles_utilisees:
            print(f"   ❌ PROBLÈME DÉTECTÉ : Rapport utilise 'count_sync' mais analyse stocke 'n_sync'")
            corrections_appliquees.append("❌ Incohérence clés rapport détectée")
        
        if 'proportion_sync' in cles_utilisees and 'p_sync' not in cles_utilisees:
            print(f"   ❌ PROBLÈME DÉTECTÉ : Rapport utilise 'proportion_sync' mais analyse stocke 'p_sync'")
            corrections_appliquees.append("❌ Incohérence clés rapport détectée")
    
    # 6. SAUVEGARDER LES CORRECTIONS (même si mineures)
    if code != original_code:
        with open('analyseur_scientifique_revolutionnaire.py', 'w', encoding='utf-8') as f:
            f.write(code)
        print(f"\n✅ Corrections appliquées et fichier sauvegardé")
    else:
        print(f"\n⚠️  Aucune correction automatique appliquée")
    
    # 7. RAPPORT FINAL
    print(f"\n📋 RAPPORT DE CORRECTION :")
    print(f"   • Backup créé : {backup_file}")
    print(f"   • Corrections appliquées : {len(corrections_appliquees)}")
    
    for correction in corrections_appliquees:
        print(f"     - {correction}")
    
    if problemes_detectes:
        print(f"\n🚨 PROBLÈMES DÉTECTÉS NÉCESSITANT CORRECTION MANUELLE :")
        for probleme in problemes_detectes:
            print(f"   • Ligne {probleme['ligne']} : {probleme['description']}")
            print(f"     Contexte : {probleme['contexte']}")
    
    return len(corrections_appliquees), problemes_detectes

def analyser_extraction_donnees():
    """Analyse spécifique de l'extraction des données"""
    print(f"\n🔍 ANALYSE SPÉCIFIQUE EXTRACTION DONNÉES")
    print("=" * 50)
    
    with open('analyseur_scientifique_revolutionnaire.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    # Rechercher la fonction extraire_sequences
    pattern = r"def extraire_sequences\(.*?\):(.*?)(?=def|\Z)"
    match = re.search(pattern, code, re.DOTALL)
    
    if match:
        extraction_code = match.group(1)
        
        print(f"📊 Analyse de la fonction extraire_sequences :")
        
        # Vérifier les types de données
        if "dtype=object" in extraction_code:
            print(f"   ✅ Utilise dtype=object pour les chaînes")
        
        if "dtype=np.int8" in extraction_code:
            print(f"   ✅ Utilise dtype=np.int8 pour les entiers")
        
        # Vérifier la validation
        if "np.unique" in extraction_code:
            print(f"   ✅ Validation avec np.unique présente")
        
        # Vérifier les conversions
        if "int(value)" in extraction_code:
            print(f"   ✅ Conversion int() pour INDEX1")
        
        if "str(value)" in extraction_code:
            print(f"   ✅ Conversion str() pour INDEX2/3/5")
    
    return True

if __name__ == "__main__":
    corrections, problemes = corriger_mapping_analyseur()
    analyser_extraction_donnees()
    
    print(f"\n🎯 RÉSUMÉ FINAL :")
    print(f"   • Corrections automatiques : {corrections}")
    print(f"   • Problèmes détectés : {len(problemes)}")
    print(f"   • Action requise : Diagnostic plus approfondi du problème de données N/A")
