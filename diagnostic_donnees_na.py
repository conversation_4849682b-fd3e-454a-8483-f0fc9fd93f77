#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DIAGNOSTIC CRITIQUE DES DONNÉES N/A
===================================

Diagnostic spécifique pour identifier pourquoi l'analyseur produit des valeurs N/A
malgré l'extraction de 663,131 mains valides.
"""

import json
import numpy as np
from datetime import datetime

def diagnostic_dataset():
    """Diagnostic complet du dataset"""
    print("🔍 DIAGNOSTIC CRITIQUE DU DATASET")
    print("=" * 60)
    
    # Charger le dataset
    try:
        with open('dataset_baccarat_lupasco_20250702_000230.json', 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        print(f"✅ Dataset chargé avec succès")
    except Exception as e:
        print(f"❌ Erreur chargement dataset : {e}")
        return
    
    # Analyser la structure
    print(f"\n📊 STRUCTURE DU DATASET :")
    print(f"   • Clés principales : {list(dataset.keys())}")
    
    if 'parties' in dataset:
        parties = dataset['parties']
        print(f"   • Nombre de parties : {len(parties)}")
        
        # Analyser quelques parties
        mains_totales = 0
        mains_valides = 0
        mains_avec_index1 = 0
        mains_avec_index2 = 0
        mains_avec_index3 = 0
        mains_avec_index5 = 0
        
        valeurs_index1 = []
        valeurs_index2 = []
        valeurs_index3 = []
        valeurs_index5 = []
        
        print(f"\n🔍 ANALYSE DES PREMIÈRES 10 PARTIES...")
        
        for i, partie in enumerate(parties[:10]):
            print(f"\n   📋 PARTIE {i+1} :")
            print(f"      • Clés : {list(partie.keys())}")
            
            if 'mains' in partie:
                print(f"      • Nombre de mains : {len(partie['mains'])}")
                
                for j, main in enumerate(partie['mains'][:5]):  # Premières 5 mains
                    mains_totales += 1
                    print(f"         Main {j+1} : {list(main.keys())}")
                    
                    # Vérifier main_number
                    main_number = main.get('main_number')
                    print(f"           main_number : {main_number}")
                    
                    if main_number is not None:
                        mains_valides += 1
                        
                        # Vérifier les index
                        for index_name in ['index1', 'index2', 'index3', 'index5']:
                            value = main.get(index_name)
                            print(f"           {index_name} : {value} (type: {type(value)})")
                            
                            if value is not None:
                                if index_name == 'index1':
                                    mains_avec_index1 += 1
                                    valeurs_index1.append(value)
                                elif index_name == 'index2':
                                    mains_avec_index2 += 1
                                    valeurs_index2.append(value)
                                elif index_name == 'index3':
                                    mains_avec_index3 += 1
                                    valeurs_index3.append(value)
                                elif index_name == 'index5':
                                    mains_avec_index5 += 1
                                    valeurs_index5.append(value)
        
        print(f"\n📊 RÉSUMÉ ÉCHANTILLON (10 premières parties) :")
        print(f"   • Mains totales : {mains_totales}")
        print(f"   • Mains valides : {mains_valides}")
        print(f"   • Mains avec INDEX1 : {mains_avec_index1}")
        print(f"   • Mains avec INDEX2 : {mains_avec_index2}")
        print(f"   • Mains avec INDEX3 : {mains_avec_index3}")
        print(f"   • Mains avec INDEX5 : {mains_avec_index5}")
        
        print(f"\n🔍 VALEURS DÉTECTÉES :")
        if valeurs_index1:
            print(f"   • INDEX1 uniques : {list(set(valeurs_index1))}")
        if valeurs_index2:
            print(f"   • INDEX2 uniques : {list(set(valeurs_index2))}")
        if valeurs_index3:
            print(f"   • INDEX3 uniques : {list(set(valeurs_index3))}")
        if valeurs_index5:
            print(f"   • INDEX5 échantillon : {valeurs_index5[:5]}")

def diagnostic_extraction_complete():
    """Diagnostic de l'extraction complète"""
    print(f"\n🔍 DIAGNOSTIC EXTRACTION COMPLÈTE")
    print("=" * 50)
    
    # Simuler l'extraction comme dans l'analyseur
    try:
        with open('dataset_baccarat_lupasco_20250702_000230.json', 'r', encoding='utf-8') as f:
            dataset = json.load(f)
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return
    
    sequences = {
        'index1': [],
        'index2': [],
        'index3': [],
        'index5': [],
        'cards_count': []
    }
    
    mains_totales = 0
    mains_valides = 0
    
    print(f"🔄 Extraction en cours...")
    
    for i, partie in enumerate(dataset['parties']):
        if i >= 100:  # Limiter pour le diagnostic
            break
            
        if 'mains' not in partie:
            continue
            
        for main in partie['mains']:
            mains_totales += 1
            
            # Vérifier si la main est valide
            if main.get('main_number') is None:
                continue
            
            mains_valides += 1
            
            # Extraire les valeurs
            for index_name in ['index1', 'index2', 'index3', 'index5', 'cards_count']:
                if index_name in main and main[index_name] is not None:
                    value = main[index_name]
                    sequences[index_name].append(value)
    
    print(f"✅ Extraction terminée (100 premières parties)")
    print(f"   • Mains totales : {mains_totales}")
    print(f"   • Mains valides : {mains_valides}")
    
    for key, seq in sequences.items():
        print(f"   • {key} : {len(seq)} éléments")
        if seq:
            unique_vals = list(set(seq))
            print(f"     → Valeurs uniques : {unique_vals[:10]}")  # Premières 10
    
    # Test de calcul SYNC/DESYNC
    if sequences['index1']:
        print(f"\n🧮 TEST CALCUL SYNC/DESYNC :")
        index1_array = np.array(sequences['index1'])
        
        # Compter selon le mapping attendu
        n_sync = np.sum(index1_array == 0)
        n_desync = np.sum(index1_array == 1)
        n_total = len(index1_array)
        
        print(f"   • Total éléments : {n_total}")
        print(f"   • SYNC (0) : {n_sync}")
        print(f"   • DESYNC (1) : {n_desync}")
        print(f"   • Autres valeurs : {n_total - n_sync - n_desync}")
        
        if n_total > 0:
            p_sync = n_sync / n_total
            p_desync = n_desync / n_total
            print(f"   • Proportion SYNC : {p_sync:.6f}")
            print(f"   • Proportion DESYNC : {p_desync:.6f}")
        
        # Vérifier les types de données
        print(f"   • Types dans INDEX1 : {[type(x) for x in sequences['index1'][:5]]}")

def diagnostic_probleme_na():
    """Diagnostic spécifique du problème N/A"""
    print(f"\n🚨 DIAGNOSTIC PROBLÈME N/A")
    print("=" * 40)
    
    print(f"🔍 HYPOTHÈSES À VÉRIFIER :")
    print(f"   1. Dataset contient des valeurs mais extraction échoue")
    print(f"   2. Extraction réussit mais calculs échouent")
    print(f"   3. Calculs réussissent mais stockage échoue")
    print(f"   4. Stockage réussit mais rapport accède aux mauvaises clés")
    print(f"   5. Types de données incompatibles (string vs int)")
    
    print(f"\n🎯 ACTIONS RECOMMANDÉES :")
    print(f"   • Vérifier les types de données dans INDEX1")
    print(f"   • Tracer l'exécution de analyser_equilibre_sync_desync()")
    print(f"   • Vérifier que self.sequences['index1'] n'est pas vide")
    print(f"   • Ajouter des prints de debug dans l'analyseur")

if __name__ == "__main__":
    diagnostic_dataset()
    diagnostic_extraction_complete()
    diagnostic_probleme_na()
    
    print(f"\n🎯 CONCLUSION :")
    print(f"   Le diagnostic révélera si le problème est dans :")
    print(f"   • La structure du dataset")
    print(f"   • L'extraction des données")
    print(f"   • Les calculs statistiques")
    print(f"   • La génération du rapport")
